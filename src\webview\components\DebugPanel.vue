<template>
  <div
    v-if="isVisible"
    class="debug-panel-simple"
  >
    <div class="debug-header-simple">
      <span>🐛 调试面板 ({{ logs.length }})</span>
      <button @click="handleClose" class="close-btn-simple">✕</button>
    </div>
    <div class="debug-content-simple">
      <div class="debug-controls">
        <button @click="testLog" class="control-btn">测试日志</button>
        <button @click="copyAllLogs" class="control-btn copy-btn">📋 复制全部</button>
        <button @click="clearLogs" class="control-btn clear-btn">🗑️ 清空</button>
      </div>
      
      <div class="logs-container">
        <div v-if="logs.length === 0" class="no-logs">
          暂无日志记录
        </div>
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-item', log.level]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span :class="['log-level', log.level]">{{ log.level.toUpperCase() }}</span>
          <span class="log-content">{{ log.content }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

/** 日志条目接口 */
interface ILogItem {
  /** 日志级别 */
  level: 'info' | 'warn' | 'error' | 'debug';
  /** 日志内容 */
  content: string;
  /** 时间戳 */
  timestamp: number;
}

/** 组件属性接口 */
interface IProps {
  /** 聊天状态数据 */
  chatState?: any;
  /** 是否显示面板 */
  isVisible?: boolean;
}

/** 组件事件接口 */
interface IEmits {
  /** 切换面板显示 */
  toggle: [];
}

const props = withDefaults(defineProps<IProps>(), {
  isVisible: false
});

const emit = defineEmits<IEmits>();

// 使用计算属性来避免响应式循环
const isVisible = computed(() => props.isVisible);

// 日志存储
const logs = ref<ILogItem[]>([]);

/** 添加日志 */
function addLog(level: ILogItem['level'], args: any[]): void {
  const content = args.map(arg => {
    if (typeof arg === 'object') {
      try {
        return JSON.stringify(arg, null, 2);
      } catch {
        return String(arg);
      }
    }
    return String(arg);
  }).join(' ');

  logs.value.push({
    level,
    content,
    timestamp: Date.now()
  });

  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.splice(0, logs.value.length - 100);
  }
}

/** 劫持Console方法 */
function interceptConsole(): void {
  const originalMethods = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    debug: console.debug,
    info: console.info
  };

  console.log = (...args: any[]) => {
    originalMethods.log(...args);
    addLog('info', args);
  };

  console.warn = (...args: any[]) => {
    originalMethods.warn(...args);
    addLog('warn', args);
  };

  console.error = (...args: any[]) => {
    originalMethods.error(...args);
    addLog('error', args);
  };

  console.debug = (...args: any[]) => {
    originalMethods.debug(...args);
    addLog('debug', args);
  };

  console.info = (...args: any[]) => {
    originalMethods.info(...args);
    addLog('info', args);
  };
}

/** 关闭面板 */
function handleClose(): void {
  emit('toggle');
}

/** 测试日志 */
function testLog(): void {
  console.log('📝 这是一条测试日志');
  console.warn('⚠️ 这是一条警告日志');
  console.error('❌ 这是一条错误日志');
}

/** 复制所有日志 */
async function copyAllLogs(): Promise<void> {
  const content = logs.value.map(log => {
    const time = new Date(log.timestamp).toLocaleTimeString();
    return `[${time}] ${log.level.toUpperCase()}: ${log.content}`;
  }).join('\n');

  try {
    await navigator.clipboard.writeText(content);
    console.log('✅ 日志已复制到剪贴板');
  } catch (error) {
    console.error('❌ 复制失败:', error);
  }
}

/** 清空日志 */
function clearLogs(): void {
  logs.value = [];
  console.log('🗑️ 日志已清空');
}

/** 格式化时间 */
function formatTime(timestamp: number): string {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 组件挂载时初始化
onMounted(() => {
  interceptConsole();
  console.log('🚀 调试面板已启动，开始监听控制台输出');
});
</script>

<style scoped>
.debug-panel-simple {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 500px;
  background: #1e1e1e;
  border: 1px solid #3e3e3e;
  border-radius: 8px;
  color: #e0e0e0;
  z-index: 9999;
  font-family: monospace;
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.debug-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2d2d2d;
  border-bottom: 1px solid #3e3e3e;
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
}

.close-btn-simple {
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn-simple:hover {
  background: #e74c3c;
  color: white;
}

.debug-content-simple {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.debug-controls {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #3e3e3e;
  background: #252525;
  flex-shrink: 0;
}

.control-btn {
  background: #4ade80;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.control-btn:hover {
  background: #22c55e;
}

.copy-btn {
  background: #3b82f6;
}

.copy-btn:hover {
  background: #2563eb;
}

.clear-btn {
  background: #ef4444;
}

.clear-btn:hover {
  background: #dc2626;
}

.logs-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  min-height: 200px;
}

.no-logs {
  text-align: center;
  color: #888;
  padding: 20px;
  font-style: italic;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 4px 8px;
  margin-bottom: 2px;
  border-radius: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.log-item.info {
  background: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

.log-item.warn {
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid #f59e0b;
}

.log-item.error {
  background: rgba(239, 68, 68, 0.1);
  border-left: 3px solid #ef4444;
}

.log-item.debug {
  background: rgba(139, 92, 246, 0.1);
  border-left: 3px solid #8b5cf6;
}

.log-time {
  color: #888;
  font-size: 10px;
  min-width: 60px;
  flex-shrink: 0;
}

.log-level {
  font-weight: bold;
  font-size: 9px;
  padding: 2px 4px;
  border-radius: 2px;
  min-width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.log-level.info {
  background: #3b82f6;
  color: white;
}

.log-level.warn {
  background: #f59e0b;
  color: white;
}

.log-level.error {
  background: #ef4444;
  color: white;
}

.log-level.debug {
  background: #8b5cf6;
  color: white;
}

.log-content {
  flex: 1;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 滚动条样式 - 精致设计 */
.logs-container::-webkit-scrollbar {
  width: 4px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(45, 45, 45, 0.5);
  border-radius: 2px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #5a5a5a, #4a4a4a);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #6a6a6a, #5a5a5a);
  transform: scaleX(1.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
</style>