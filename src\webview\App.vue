<template>
  <div id="app" class="app-container">
    <ChatContainer />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import ChatContainer from './components/ChatContainer.vue'

/**
 * 应用初始化
 */
onMounted(() => {
  // 通知VSCode扩展webview已准备就绪
  const vscode = window.acquireVsCodeApi()
  vscode.postMessage({
    type: 'webviewReady'
  })

  // 设置VSCode主题类
  updateThemeClass()

  // 强制注入滚动条样式
  injectScrollbarStyles()

  // 监听主题变化
  const observer = new MutationObserver(() => {
    updateThemeClass()
  })

  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['class', 'data-vscode-theme-kind']
  })
})

/**
 * 强制注入滚动条样式
 */
const injectScrollbarStyles = () => {
  const style = document.createElement('style')
  style.id = 'custom-scrollbar-styles'
  style.innerHTML = `
    /* 强制滚动条样式 - 动态注入 */
    * ::-webkit-scrollbar {
      width: 6px !important;
      height: 6px !important;
    }

    * ::-webkit-scrollbar-track {
      background: rgba(30, 30, 30, 0.3) !important;
      border-radius: 3px !important;
    }

    * ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #6495ED 0%, #4682B4 100%) !important;
      border-radius: 3px !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
    }

    * ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #87CEEB 0%, #6495ED 100%) !important;
      transform: scaleX(1.3) !important;
      box-shadow: 0 2px 8px rgba(100, 149, 237, 0.4) !important;
    }
  `

  // 移除旧的样式（如果存在）
  const existingStyle = document.getElementById('custom-scrollbar-styles')
  if (existingStyle) {
    existingStyle.remove()
  }

  // 添加新样式到head
  document.head.appendChild(style)

  console.log('🎨 滚动条样式已强制注入')
}

/**
 * 更新主题类名
 */
const updateThemeClass = () => {
  const body = document.body
  const themeKind = body.getAttribute('data-vscode-theme-kind')
  
  // 移除现有主题类
  body.classList.remove('vscode-light', 'vscode-dark', 'vscode-high-contrast')
  
  // 添加对应主题类
  switch (themeKind) {
    case 'vscode-light':
      body.classList.add('vscode-light')
      document.documentElement.classList.remove('dark')
      break
    case 'vscode-dark':
      body.classList.add('vscode-dark')
      document.documentElement.classList.add('dark')
      break
    case 'vscode-high-contrast':
      body.classList.add('vscode-high-contrast')
      document.documentElement.classList.add('dark')
      break
    default:
      // 默认使用深色主题
      body.classList.add('vscode-dark')
      document.documentElement.classList.add('dark')
  }
}
</script>

<style>
/* 全局样式 */
.app-container {
  @apply w-full h-screen overflow-hidden;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  color: var(--vscode-foreground);
  background-color: var(--vscode-editor-background);
}

/* VSCode主题适配 */
.vscode-light {
  color-scheme: light;
}

.vscode-dark {
  color-scheme: dark;
}

.vscode-high-contrast {
  color-scheme: dark;
}

/* 确保全屏显示 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 禁用文本选择（可选） */
.app-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许消息内容选择 */
.message-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 允许输入框选择 */
.input-field {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 强制滚动条样式 - 最高优先级 */
:deep(*)::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

:deep(*)::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.3) !important;
  border-radius: 3px !important;
}

:deep(*)::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6495ED 0%, #4682B4 100%) !important;
  border-radius: 3px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
}

:deep(*)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #87CEEB 0%, #6495ED 100%) !important;
  transform: scaleX(1.3) !important;
  box-shadow: 0 2px 8px rgba(100, 149, 237, 0.4) !important;
}
</style>
